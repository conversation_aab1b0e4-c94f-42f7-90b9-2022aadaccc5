'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import { getPublicCTAAnalytics } from '@/services/recipeService';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';

export default function RecipeCTAReportsTable({
  searchValue = '',
  filters = {},
}) {
  // State management
  const [loader, setLoader] = useState(false);
  const [ctaData, setCTAData] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Get CTA Analytics data from API
  const getCTAAnalyticsData = useCallback(
    async (search = '', pageNo = 1, Rpp = rowsPerPage, filterData = {}) => {
      setLoader(true);
      try {
        // Create filter object with search as recipe_name and only required filters
        const apiFilter = {
          ...(search && { recipe_name: search }),
          ...(filterData?.ctaType && { cta_type: filterData?.ctaType }),
          ...(filterData?.dateRange && { date_range: filterData?.dateRange }),
        };

        const { data, totalRecords } = await getPublicCTAAnalytics(
          '', // Don't pass search as separate parameter
          pageNo,
          apiFilter,
          Rpp,
          { key: '', value: 'ASC' } // Default sort
        );

        const transformedData = data?.map((item, index) => ({
          id: `${item?.recipe_id}-${item?.cta_type?.replace(/\s+/g, '_')}`,
          sequentialId: (pageNo - 1) * Rpp + index + 1,
          recipeName: item?.recipe_name || '-',
          ctaType: item?.cta_type || '-',
          clicks: item?.clicks || 0,
          lastClickedAt: item?.last_clicked_at,
        }));

        setCTAData(transformedData || []);
        setTotalCount(totalRecords || 0);
        setPage(pageNo);
      } catch (error) {
        setApiMessage(
          'error',
          error?.response?.data?.message || 'Failed to fetch CTA analytics data'
        );
        setCTAData([]);
        setTotalCount(0);
      } finally {
        setLoader(false);
      }
    },
    [rowsPerPage]
  );

  // Handle page change
  const handlePageChange = (newPage) => {
    getCTAAnalyticsData(searchValue, newPage, rowsPerPage, filters);
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    getCTAAnalyticsData(searchValue, 1, newRowsPerPage, filters);
  };

  // Load data when component mounts or filters change
  useEffect(() => {
    getCTAAnalyticsData(searchValue, 1, rowsPerPage, filters);
  }, [searchValue, filters, getCTAAnalyticsData]);

  // CommonTable columns configuration
  const columns = [
    {
      header: 'ID',
      accessor: 'sequentialId',
      sortable: false,
    },
    {
      header: 'Recipe Name',
      accessor: 'recipeName',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'CTA Type',
      accessor: 'ctaType',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Clicks',
      accessor: 'clicks',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Last Clicked At',
      accessor: 'lastClickedAt',
      sortable: false,
      renderCell: (value) => (value ? DateFormat(value, 'datesWithhour') : '-'),
    },
  ];

  return (
    <Box className="report-table-container">
      {loader ? (
        <ContentLoader />
      ) : ctaData && ctaData?.length === 0 ? (
        <NoDataView
          title="No CTA Analytics Found"
          description="There are no CTA analytics available at the moment."
        />
      ) : (
        <CommonTable
          columns={columns}
          data={ctaData}
          totalCount={totalCount}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          showPagination={true}
        />
      )}
    </Box>
  );
}
