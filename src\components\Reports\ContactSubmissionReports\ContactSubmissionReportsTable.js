'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import Icon from '@/components/UI/AppIcon/AppIcon';
import {
  getPublicContactAnalytics,
  deleteContactSubmission,
} from '@/services/recipeService';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';

export default function ContactSubmissionReportsTable({
  searchValue = '',
  filters = {},
}) {
  // State management
  const [loader, setLoader] = useState(false);
  const [contactData, setContactData] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Delete modal state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return DateFormat(dateString, 'datesWithhour');
  };

  // Get Contact Analytics data from API
  const getContactAnalyticsData = useCallback(
    async (search = '', pageNo = 1, Rpp = rowsPerPage, filterData = {}) => {
      setLoader(true);
      try {
        // Create filter object following ContactSubmissionsTable pattern
        const apiFilter = {
          ...(search && { recipe_name: search }), // Pass search as recipe_name
          ...(filterData?.recipeName && { recipe: filterData?.recipeName }),
          ...(filterData?.dateRange && { date_range: filterData?.dateRange }),
        };

        const { data, totalRecords } = await getPublicContactAnalytics(
          pageNo,
          apiFilter,
          Rpp,
          { key: '', value: 'ASC' } // Default sort
        );

        // Transform API data to match table structure
        const transformedData =
          data?.map((item, index) => ({
            id: item?.id || item?.submission_id || index + 1,
            sequentialId: (pageNo - 1) * Rpp + index + 1,
            recipeName: item?.recipe_name || `Recipe ${item?.recipe_id}`,
            name: item?.name || '-',
            email: item?.email || '-',
            mobile: item?.mobile || '-',
            message: item?.message || '-',
            submittedOn: formatDate(item?.submitted_on),
          })) || [];

        setContactData(transformedData);
        setTotalCount(totalRecords || 0);
        setPage(pageNo);
      } catch (error) {
        setApiMessage(
          'error',
          error?.response?.data?.message ||
            'Failed to fetch contact submissions data'
        );
        setContactData([]);
        setTotalCount(0);
      } finally {
        setLoader(false);
      }
    },
    [rowsPerPage]
  );

  // Handle page change
  const handlePageChange = (newPage) => {
    getContactAnalyticsData(searchValue, newPage, rowsPerPage, filters);
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    getContactAnalyticsData(searchValue, 1, newRowsPerPage, filters);
  };

  // Handle delete
  const handleDelete = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    try {
      await deleteContactSubmission(deleteId);
      setApiMessage('success', 'Contact submission deleted successfully');

      // Refresh data after deletion
      await getContactAnalyticsData(searchValue, page, rowsPerPage, filters);

      setDeleteDialogOpen(false);
      setDeleteId(null);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to delete contact submission'
      );
    }
  };

  // Load initial data on component mount
  useEffect(() => {
    getContactAnalyticsData('', 1, rowsPerPage, {});
  }, []); // Only run on mount

  // Load data when search or filters change
  useEffect(() => {
    if (searchValue || Object.keys(filters).some((key) => filters[key])) {
      getContactAnalyticsData(searchValue, 1, rowsPerPage, filters);
    }
  }, [searchValue, filters]);

  // Action menu items for each row
  const getActionMenuItems = (row) => {
    const menuItems = [];

    // Add delete option (no permission check needed based on original implementation)
    menuItems.push({
      label: 'Delete',
      icon: <Icon name="Trash2" size={16} />,
      variant: 'danger',
      onClick: () => handleDelete(row?.id),
    });

    return menuItems;
  };

  // CommonTable columns configuration
  const columns = [
    {
      header: 'ID',
      accessor: 'sequentialId',
      sortable: false,
    },
    {
      header: 'Recipe Name',
      accessor: 'recipeName',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Name',
      accessor: 'name',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Email',
      accessor: 'email',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Mobile',
      accessor: 'mobile',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Message',
      accessor: 'message',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Submitted On',
      accessor: 'submittedOn',
      sortable: false,
      renderCell: (value) => value,
    },
  ];

  return (
    <Box className="report-table-container">
      {loader ? (
        <ContentLoader />
      ) : contactData && contactData?.length === 0 ? (
        <NoDataView
          title="No Contact Submissions Found"
          description="There are no contact form submissions available at the moment."
        />
      ) : (
        <CommonTable
          columns={columns}
          data={contactData}
          pageSize={rowsPerPage}
          actionMenuItems={getActionMenuItems}
          paginationProps={{
            totalCount,
            currentPage: page,
            rowsPerPage,
            onPageChange: handlePageChange,
            OnRowPerPage: handleRowsPerPageChange,
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <DialogBox
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        title="Delete Contact Submission"
        maxWidth="sm"
      >
        <DeleteModal
          title="Are you sure you want to delete this contact submission?"
          description="This action cannot be undone."
          onCancel={() => setDeleteDialogOpen(false)}
          onConfirm={confirmDelete}
        />
      </DialogBox>
    </Box>
  );
}
