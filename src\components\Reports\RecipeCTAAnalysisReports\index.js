'use client';
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import RecipeCTAAnalyticsTable from '@/components/Recipes/RecipeDashboard/admin-analytics-dashboard/components/RecipeCTAAnalyticsTable';
import { staticOptions } from '@/helper/common/staticOptions';

export default function RecipeCTAAnalysisReports() {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    ctaType: '',
    dateRange: null,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Recipe Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'ctaType',
      type: 'select',
      label: 'CTA Type',
      options: staticOptions?.ctaTypes || [
        { label: 'All Types', value: '' },
        { label: 'Contact Form', value: 'contact_form' },
        { label: 'Recipe View', value: 'recipe_view' },
        { label: 'Download', value: 'download' },
      ],
    },
    {
      name: 'dateRange',
      type: 'dateRange',
      label: 'Date Range',
    },
  ];

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  return (
    <Box className="report-main-container">
      <Box className="report-header">
        <Typography variant="h6" className="report-title">
          Recipe CTA Analysis Reports
        </Typography>
      </Box>

      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <Box className="report-table-container">
        <RecipeCTAAnalyticsTable searchValue={searchValue} filters={filters} />
      </Box>
    </Box>
  );
}
